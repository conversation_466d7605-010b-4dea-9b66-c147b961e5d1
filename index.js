import 'dotenv/config'
import express from 'express';
import cors from 'cors';
import nodemailer from 'nodemailer';

const app = express();

// CORS setup (allow all origins — for testing only)
const corsOptions = {
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
};

app.use(cors(corsOptions));
app.use(express.json());

// Test route
app.get('/', (req, res) => {
  res.send('✅ Express Server is running on Vercel!');
});

// Function to create custom email template
function createEmailTemplate(name, message, avatar = null, customName = null) {
  const senderName = customName || name;
  const avatarUrl = avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(senderName)}&size=80&background=4f46e5&color=ffffff&bold=true`;

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>New Message</title>
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background: white; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 30px; text-align: center; }
        .header h1 { color: white; font-size: 28px; margin-bottom: 10px; }
        .header p { color: rgba(255,255,255,0.9); font-size: 16px; }
        .content { padding: 40px 30px; }
        .sender-info { display: flex; align-items: center; margin-bottom: 30px; padding: 20px; background: #f8fafc; border-radius: 12px; border-left: 4px solid #667eea; }
        .avatar { width: 80px; height: 80px; border-radius: 50%; margin-right: 20px; border: 3px solid #667eea; }
        .sender-details h3 { color: #1e293b; font-size: 20px; margin-bottom: 5px; }
        .sender-details p { color: #64748b; font-size: 14px; }
        .message-box { background: #ffffff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 25px; margin: 20px 0; }
        .message-box h4 { color: #1e293b; margin-bottom: 15px; font-size: 18px; }
        .message-text { color: #475569; line-height: 1.6; font-size: 16px; }
        .footer { background: #1e293b; color: white; padding: 30px; text-align: center; }
        .footer p { margin-bottom: 10px; }
        .footer a { color: #667eea; text-decoration: none; }
        .divider { height: 1px; background: linear-gradient(to right, transparent, #e2e8f0, transparent); margin: 30px 0; }
        @media (max-width: 600px) {
          .sender-info { flex-direction: column; text-align: center; }
          .avatar { margin-right: 0; margin-bottom: 15px; }
          .content { padding: 20px; }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div style="margin-bottom: 20px;">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" viewBox="0 0 256 257">
              <defs>
                <linearGradient id="IconifyId1813088fe1fbc01fb466" x1="-.828%" x2="57.636%" y1="7.652%" y2="78.411%">
                  <stop offset="0%" stop-color="#41D1FF"></stop>
                  <stop offset="100%" stop-color="#BD34FE"></stop>
                </linearGradient>
                <linearGradient id="IconifyId1813088fe1fbc01fb467" x1="43.376%" x2="50.316%" y1="2.242%" y2="89.03%">
                  <stop offset="0%" stop-color="#FFEA83"></stop>
                  <stop offset="8.333%" stop-color="#FFDD35"></stop>
                  <stop offset="100%" stop-color="#FFA800"></stop>
                </linearGradient>
              </defs>
              <path fill="url(#IconifyId1813088fe1fbc01fb466)" d="M255.153 37.938L134.897 252.976c-2.483 4.44-8.862 4.466-11.382.048L.875 37.958c-2.746-4.814 1.371-10.646 6.827-9.67l120.385 21.517a6.537 6.537 0 0 0 2.322-.004l117.867-21.483c5.438-.991 9.574 4.796 6.877 9.62Z"></path>
              <path fill="url(#IconifyId1813088fe1fbc01fb467)" d="M185.432.063L96.44 17.501a3.268 3.268 0 0 0-2.634 3.014l-5.474 92.456a3.268 3.268 0 0 0 3.997 3.378l24.777-5.718c2.318-.535 4.413 1.507 3.936 3.838l-7.361 36.047c-.495 2.426 1.782 4.5 4.151 3.78l15.304-4.649c2.372-.72 4.652 1.36 4.15 3.788l-11.698 56.621c-.732 3.542 3.979 5.473 5.943 2.437l1.313-2.028l72.516-144.72c1.215-2.423-.88-5.186-3.54-4.672l-25.505 4.922c-2.396.462-4.435-1.77-3.759-4.114l16.646-57.705c.677-2.35-1.37-4.583-3.769-4.113Z"></path>
            </svg>
          </div>
          <h1>📧 SeraProgrammer</h1>
          <p>You have received a new message through SeraProgrammer contact form</p>
        </div>

        <div class="content">
          <div class="sender-info">
            <img src="${avatarUrl}" alt="${senderName}" class="avatar">
            <div class="sender-details">
              <h3>${senderName}</h3>
              <p>📅 ${new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}</p>
            </div>
          </div>

          <div class="divider"></div>

          <div class="message-box">
            <h4>💬 Message Content:</h4>
            <div class="message-text">${message.replace(/\n/g, '<br>')}</div>
          </div>
        </div>

        <div class="footer">
          <p><strong>🤖 Automated Email Service</strong></p>
          <p>This email was sent automatically from your contact form</p>
          <p>Powered by <a href="https://seraprogrammer.com">SeraProgrammer</a></p>
        </div>
      </div>
    </body>
    </html>
  `;
}

// Email sending route - accepts email in request body with custom template options
app.post('/api/email', async (req, res) => {
  const { email, name, message, avatar, customName } = req.body;

  if (!email || !name || !message) {
    return res.status(400).json({ error: '❌ Missing email, name, or message' });
  }

  try {
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.SMTP_EMAIL,
        pass: process.env.SMTP_PASS,
      },
    });

    const displayName = customName || name;
    const htmlTemplate = createEmailTemplate(name, message, avatar, customName);

    const mailOptions = {
      from: `"SeraProgrammer" <${process.env.SMTP_EMAIL}>`,
      to: email,
      subject: `📧 New message from ${displayName} - SeraProgrammer`,
      html: htmlTemplate,
    };

    await transporter.sendMail(mailOptions);

    res.status(200).json({
      success: true,
      message: '✅ Email sent successfully with custom template',
      details: {
        to: email,
        from: displayName,
        hasCustomAvatar: !!avatar,
        hasCustomName: !!customName
      }
    });
  } catch (error) {
    console.error('❌ Email sending failed:', error);
    res.status(500).json({ success: false, error: '❌ Failed to send email', details: error.message });
  }
});

// Start server for local development
const PORT = process.env.PORT || 3000;
if (process.env.NODE_ENV !== 'production') {
  app.listen(PORT, () => {
    console.log(`🚀 Server running on http://localhost:${PORT}`);
  });
}

export default app;
