fetch('https://email-tau-five.vercel.app/api/email', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>', // Recipient email
    name: '<PERSON><PERSON><PERSON>', // Original sender name
    message: 'Hello! This is a test message with custom template features.\n\nThis email includes:\n✅ Custom avatar\n✅ Beautiful styling\n✅ Professional layout\n✅ Responsive design',
    avatar: 'https://avatars.githubusercontent.com/u/1?v=4', // Optional: Custom avatar URL
    customName: '<PERSON>kra<PERSON>' // Optional: Display name (different from original name)
  })
})
.then(res => {
  if (!res.ok) {
    // Response not OK, print status and text for debugging
    return res.text().then(text => {
      throw new Error(`HTTP ${res.status} - ${text}`);
    });
  }
  return res.json();
})
.then(data => console.log('Response:', data))
.catch(err => console.error('Fetch error:', err));
