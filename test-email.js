// Test script to send email via Vercel deployment
async function testEmail() {
  try {
    console.log('🚀 Testing email API...');
    
    const response = await fetch('https://email-tau-five.vercel.app/api/email', {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>', // Recipient email
        name: 'Test User',
        message: 'This is a test message from Vercel deployment!'
      })
    });

    console.log('📡 Response status:', response.status);
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Error response:', errorText);
      throw new Error(`HTTP ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('✅ Success response:', data);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('❌ Full error:', error);
  }
}

// Run the test
testEmail();
